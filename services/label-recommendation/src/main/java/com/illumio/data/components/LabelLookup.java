package com.illumio.data.components;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.benmanes.caffeine.cache.AsyncCache;
import com.illumio.data.util.FlowDataValidator;
import com.illumio.data.util.FlowDataValidator.ValidationResult;
import com.illumio.data.util.MetricsUtil;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.metrics.LongCounter;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.redis.core.ReactiveRedisOperations;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.Tuple2;

import java.util.Optional;
import java.util.Set;

@Component
@RequiredArgsConstructor
@Slf4j
public class LabelLookup {

    private final ObjectMapper objectMapper;
    private final AsyncCache<String, String> labelsCache;
    private final ReactiveRedisOperations<String, String> reactiveRedisOperations;
    private final MetricsUtil metricsUtil;

    private static final Set<String> RESOURCE_PROVIDER_VM_IDENTIFIER = Set.of(
            "Microsoft.Compute/virtualMachineScaleSets/virtualMachines",
            "Microsoft.Compute/virtualMachineScaleSets",
            "Microsoft.Compute/virtualMachines",
            "AWS::EC2::Instance");

    private static final String INCOMING_SERVICE_LABEL_KEY = "servicerole";
    private static final String INCOMING_USER_DEFINED_LABEL_KEY = "role";

    @Getter
    @RequiredArgsConstructor
    private enum ResourceDirection {
        SOURCE("CSSrcId", "SrcResourceType", "SrcCSLabels", "SourceLabel"),
        DESTINATION("CSDestId", "DestResourceType", "DestCSLabels", "DestinationLabel");

        private final String idKey;
        private final String typeKey;
        private final String incomingLabelsKey;
        private final String inferredLabelKey;
    }

    private void addCountMetricsPerTenant(JsonNode resourceIdFlow) {
        String illumioTenantId = "Unknown";
        try {
            // Extract IllumioTenantId value
            illumioTenantId = resourceIdFlow.get(FlowDataValidator.ILLUMIO_TENANT_ID_KEY).asText();
        } catch (Exception e) {
            log.error("Error extracting IllumioTenantId for record : {}", resourceIdFlow, e);
        }
        metricsUtil.incrementLabelRecommendationEvent(
            Attributes.builder().put(FlowDataValidator.INSIGHTS_TENANT_ID, illumioTenantId).build());

        ValidationResult validationResult = FlowDataValidator.validate(resourceIdFlow.toString());
        if (!validationResult.isValid()) {
            metricsUtil.incrementLabelRecommendationWrongEvent(
                Attributes.builder().put(FlowDataValidator.INSIGHTS_TENANT_ID, illumioTenantId)
                    .put("error_code", validationResult.getErrorCode())
                    .build());
            log.error("Invalid JSON: {}, detail: {}", resourceIdFlow, validationResult.getDetailMessage());
        }
    }

    /**
     * Returns the enriched record containing the labels.
     * In any case, if the label is not available, the record is left un-enriched.
     * The precedence of labels(user-defined, CS, ML) are defined as below:
     * - For VMs: Src/DestCSLabels.role in incoming flow -> ML label for resource_id
     * - For non-VMs: Src/DestCSLabels.role in incoming flow -> Src/DestCSLabels.servicerole in incoming flow
     * The ML labels, if required, are fetched from the local cache with external Redis cache acting as the fallback.
     *
     * @param decoratedFlow Record received from the input topic
     * @return Enriched record with the label for the source resource and destination resource
     */
    public Mono<JsonNode> enrichWithLabel(JsonNode decoratedFlow) {
        this.addCountMetricsPerTenant(decoratedFlow);

        if (!decoratedFlow.isObject()) {
            log.error("Skipping enrichment. Cannot enrich the record as it is not an object.");
            return Mono.just(decoratedFlow);
        }

        Mono<String> sourceResourceLabel = getLabel(decoratedFlow, ResourceDirection.SOURCE);
        Mono<String> dstResourceLabel = getLabel(decoratedFlow, ResourceDirection.DESTINATION);

        return Mono.zip(sourceResourceLabel.defaultIfEmpty(""), dstResourceLabel.defaultIfEmpty(""))
                .map((Tuple2<String, String> tuple) -> {
                    if (!Strings.isBlank(tuple.getT1())) {
                        ((ObjectNode) decoratedFlow).put(ResourceDirection.SOURCE.getInferredLabelKey(), tuple.getT1());
                    }
                    if (!Strings.isBlank(tuple.getT2())) {
                        ((ObjectNode) decoratedFlow).put(ResourceDirection.DESTINATION.getInferredLabelKey(), tuple.getT2());
                    }
                    // Capture non-enrichment/enrichment metrics
                    if (!decoratedFlow.has(ResourceDirection.SOURCE.getInferredLabelKey()) &&
                            !decoratedFlow.has(ResourceDirection.DESTINATION.getInferredLabelKey())) {
                        log.debug("Unable to enrich the record due to missing labels.");
                        metricsUtil.incrementRecordsUnenrichedMissingLabels();
                    } else {
                        metricsUtil.incrementRecordsEnriched();
                    }
                    return decoratedFlow;
                });
    }

    private Mono<String> getLabel(final JsonNode decoratedFlow, ResourceDirection type) {
        // Determine whether resource is a VM or not
        boolean isVm = Optional.ofNullable(decoratedFlow.get(type.getTypeKey()))
                .map(JsonNode::asText)
                .filter(RESOURCE_PROVIDER_VM_IDENTIFIER::contains)
                .isPresent();

        // Parse user-defined label and non-VM service label
        Optional<JsonNode> csLabels = Optional.ofNullable(decoratedFlow.get(type.getIncomingLabelsKey()))
                .flatMap(csLabelsText -> {
                    try {
                        return Optional.ofNullable(objectMapper.readTree(csLabelsText.asText()));
                    } catch (JsonProcessingException e) {
                        log.warn("Unable to parse incoming labels in {} as it is not an object.",
                                type.getIncomingLabelsKey());
                        return Optional.empty();
                    }
                });
        Optional<String> userDefinedLabel = csLabels
                .map(csLabelsJson -> csLabelsJson.get(INCOMING_USER_DEFINED_LABEL_KEY))
                .map(JsonNode::asText)
                .filter(s -> !s.isEmpty());
        Optional<String> serviceLabel = csLabels
                .map(csLabelsJson -> csLabelsJson.get(INCOMING_SERVICE_LABEL_KEY))
                .map(JsonNode::asText)
                .filter(s -> !s.isEmpty());

        Optional<String> resourceId = Optional.ofNullable(decoratedFlow.get(type.getIdKey()))
                .map(JsonNode::asText)
                .filter(s -> !s.isEmpty());

        if (isVm) {
            // Precedence for VMs: Src/DestCSLabels.role in incoming flow -> ML label for resource_id
            return userDefinedLabel
                    .map(Mono::just)
                    .orElseGet(() -> resourceId.map(this::getLabelFromLocalCache).orElseGet(Mono::empty));
        } else {
            // Precedence for non-VMs: Src/DestCSLabels.role in incoming flow -> Src/DestCSLabels.servicerole in incoming flow
            return userDefinedLabel
                    .map(Mono::just)
                    .orElseGet(() -> Mono.justOrEmpty(serviceLabel));
        }
    }

    @SneakyThrows
    public Mono<String> getLabelFromLocalCache(final String resourceId) {
        return Mono.fromFuture(
                labelsCache.get(
                        resourceId,
                        (resId, executor) -> getLabelFromExternalCache(resId)
                                .switchIfEmpty(Mono.just(""))
                                .subscribeOn(Schedulers.fromExecutor(executor))
                                .toFuture()));
    }

    public Mono<String> getLabelFromExternalCache(final String resourceId) {
        log.debug("Local Cache Miss: Fetching label for resource_id={} from external cache", resourceId);
        return reactiveRedisOperations
                .opsForValue()
                .get(resourceId)
                .flatMap(label -> {
                    log.debug("External Cache Hit: Fetched label={} for resource_id={} from external cache", label, resourceId);
                    return Mono.just(label);
                })
                .switchIfEmpty(Mono.defer(() -> {
                    log.debug("External Cache Miss: Label unavailable for resource_id={}", resourceId);
                    return Mono.empty();
                }))
                .onErrorResume(e -> {
                    log.error("Error fetching label from the external cache for resource_id {} : {}", resourceId, e.getMessage());
                    return Mono.empty();
                });
    }
}
